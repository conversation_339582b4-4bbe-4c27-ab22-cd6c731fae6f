server:
  port: 7091

spring:
  application:
    name: seata-server

logging:
  config: classpath:logback-spring.xml
  file:
    path: ${user.home}/logs/seata
  extend:
    logstash-appender:
      destination: 127.0.0.1:4560
    kafka-appender:
      bootstrap-servers: 127.0.0.1:9092
      topic: logback_to_logstash

console:
  user:
    username: seata
    password: seata

seata:
  config:
    # support: nacos, consul, apollo, zk, etcd3
    type: nacos
    server-addr: 127.0.0.1:8848  # 你的 Nacos 地址
    namespace: ""  # 命名空间，默认为 public
    group: SEATA_GROUP  # 配置分组
    #username: nacos  # 如果有认证
    #password: nacos
  registry:
    # support: nacos, eureka, redis, zk, consul, etcd3, sofa
    type: nacos
    nacos:
        application: seata-server
        server-addr: 127.0.0.1:8848
        group: SEATA_GROUP  # 与客户端保持一致
        namespace: ""
        cluster: project-a-group  # 集群名称
        #username: nacos
        #password: nacos
  store:
    # support: file 、 db 、 redis
    mode: db
    db:
      driverClassName: 'com.mysql.cj.jdbc.Driver'
      url: '****************************************'
      user: 'root'
      password: 'root'
#  server:
#    service-port: 8091 #If not configured, the default is '${server.port} + 1000'
  security:
    secretKey: SeataSecretKey0c382ef121d778043159209298fd40bf3850a017
    tokenValidityInMilliseconds: 1800000
    ignore:
      urls: /,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-fe/public/**,/api/v1/auth/login
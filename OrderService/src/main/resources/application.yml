server:
  port: 9093
spring:
  application:
    name: order-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************
    username: root
    password: root
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848

seata:
  registry:
    type: nacos
    nacos:
      server-addr: 127.0.0.1:8848
      # 空字符串表示使用 nacos 的默认 namespace（public）
      namespace: ""
      group: SEATA_GROUP
      application: seata-server
      username: nacos
      password: nacos
  tx-service-group: my_tx_group
  service:
    vgroup-mapping:
      my_tx_group: project-a-group


  # 这里配置 Seata 使用 AT 模式（默认值也是 AT 模式）
  data-source-proxy-mode: AT


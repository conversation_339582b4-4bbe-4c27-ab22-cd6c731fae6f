/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    id 'buildlogic.java-conventions'
}

dependencies {
    implementation libs.org.springframework.boot.spring.boot.starter.web
    implementation libs.com.alibaba.cloud.spring.cloud.starter.alibaba.nacos.discovery
    implementation libs.org.springframework.cloud.spring.cloud.starter.openfeign
    implementation libs.org.springframework.cloud.spring.cloud.starter.loadbalancer
    implementation libs.com.mysql.mysql.connector.j
    implementation libs.com.baomidou.mybatis.plus.boot.starter
    implementation libs.com.alibaba.cloud.spring.cloud.starter.alibaba.seata
    implementation libs.io.seata.seata.spring.boot.starter
    compileOnly libs.org.projectlombok.lombok
}

description = 'Order Service'

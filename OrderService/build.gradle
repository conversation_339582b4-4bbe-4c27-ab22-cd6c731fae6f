description = 'Order Service - 订单服务，负责订单创建和分布式事务协调'

dependencies {
    // Spring Boot Web
    implementation libs.spring.boot.starter.web

    // Nacos 服务发现
    implementation libs.nacos.discovery

    // OpenFeign 服务调用
    implementation libs.spring.cloud.starter.openfeign

    // 负载均衡
    implementation libs.spring.cloud.starter.loadbalancer

    // 数据库相关
    implementation libs.mysql.connector
    implementation libs.mybatis.plus

    // Seata 分布式事务
    implementation libs.alibaba.seata
    implementation libs.seata.starter
}

#!/bin/bash

echo "========================================"
echo "        IDE 问题修复脚本"
echo "========================================"
echo

echo "1. 清理 Gradle 缓存..."
./gradlew clean

echo
echo "2. 刷新依赖..."
./gradlew build --refresh-dependencies

echo
echo "3. 生成 IDE 文件..."
./gradlew idea

echo
echo "4. 验证编译..."
./gradlew compileJava

echo
echo "========================================"
echo "修复完成！请按照以下步骤操作："
echo
echo "IntelliJ IDEA:"
echo "1. 安装 Lombok 插件"
echo "2. 启用注解处理 (Settings > Annotation Processors)"
echo "3. 重新导入项目 (File > Open > 选择 build.gradle)"
echo
echo "VS Code:"
echo "1. 安装 Extension Pack for Java"
echo "2. 安装 Lombok Annotations Support"
echo "3. 重新加载项目 (Ctrl+Shift+P > Java: Reload Projects)"
echo
echo "Eclipse:"
echo "1. 安装 Lombok (运行 java -jar lombok.jar)"
echo "2. 重新导入项目"
echo "========================================"

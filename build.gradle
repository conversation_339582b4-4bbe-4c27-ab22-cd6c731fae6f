plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.18' apply false
    id 'io.spring.dependency-management' version '1.0.15.RELEASE' apply false
}

allprojects {
    group = 'com.jobs'
    version = '1.0'
    
    repositories {
        mavenLocal()
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://repo.maven.apache.org/maven2/' }
        mavenCentral()
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    
    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }
    
    dependencyManagement {
        imports {
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:2021.0.9"
            mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:2021.0.6.2"
        }
    }
    
    dependencies {
        // 公共依赖 - Lombok
        compileOnly 'org.projectlombok:lombok:1.18.34'
        annotationProcessor 'org.projectlombok:lombok:1.18.34'
        testCompileOnly 'org.projectlombok:lombok:1.18.34'
        testAnnotationProcessor 'org.projectlombok:lombok:1.18.34'

        // 测试依赖
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
    }
    
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }
    
    tasks.withType(Javadoc) {
        options.encoding = 'UTF-8'
    }
    
    test {
        useJUnitPlatform()
    }
}

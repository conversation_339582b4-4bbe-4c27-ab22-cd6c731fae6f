# IDE 配置指南 - 完整解决方案

## 问题描述

如果您在 IDE 中看到类似以下错误：
```
Cannot resolve symbol 'log'
java.lang.NoClassDefFoundError: org/slf4j/LoggerFactory
Exception in thread "main" java.lang.NoClassDefFoundError: org/slf4j/LoggerFactory
```

这些问题的根本原因是：
1. Lombok 的 `@Slf4j` 注解没有被正确处理
2. IDE 使用了混合的 classpath（Maven + Gradle）
3. 缺少 SLF4J 运行时实现

## 🚀 快速修复方案

### 方法一：使用修复脚本（推荐）

**Windows 用户：**
```bash
./fix-ide.bat
```

**Linux/Mac 用户：**
```bash
./fix-ide.sh
```

### 方法二：手动修复

## 解决方案

### IntelliJ IDEA

1. **安装 Lombok 插件**
   - 打开 `File` → `Settings` → `Plugins`
   - 搜索 "Lombok" 并安装
   - 重启 IDEA

2. **启用注解处理**
   - 打开 `File` → `Settings` → `Build, Execution, Deployment` → `Compiler` → `Annotation Processors`
   - 勾选 `Enable annotation processing`
   - 点击 `Apply` 和 `OK`

3. **刷新 Gradle 项目**
   - 在 Gradle 工具窗口中点击刷新按钮
   - 或者执行 `./gradlew clean build --refresh-dependencies`

4. **重新导入项目**
   - 关闭项目
   - 选择 `Open` 并选择项目根目录的 `build.gradle` 文件
   - 选择 "Open as Project"

### Visual Studio Code

1. **安装必要的扩展**
   - Extension Pack for Java
   - Lombok Annotations Support for VS Code

2. **配置设置**
   - 项目已包含 `.vscode/settings.json` 配置文件
   - 确保 Java 扩展正确识别 Gradle 项目

3. **刷新项目**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 执行 `Java: Reload Projects`

### Eclipse

1. **安装 Lombok**
   - 下载 lombok.jar
   - 运行 `java -jar lombok.jar`
   - 选择 Eclipse 安装目录并安装

2. **导入 Gradle 项目**
   - `File` → `Import` → `Gradle` → `Existing Gradle Project`
   - 选择项目根目录

## 验证配置

配置完成后，您应该能够：

1. **看到 log 变量**
   - 在使用 `@Slf4j` 注解的类中，`log` 变量应该被识别
   - 没有红色下划线错误

2. **自动补全工作**
   - 输入 `log.` 应该显示可用的方法（info, debug, error 等）

3. **编译成功**
   - 项目应该能够正常编译和运行

## 常见问题

### Q: 仍然看到 "Cannot resolve symbol 'log'" 错误
**A:** 
1. 确保 Lombok 插件已安装并启用
2. 检查注解处理是否启用
3. 重新导入/刷新 Gradle 项目
4. 重启 IDE

### Q: 编译时出现 Lombok 相关错误
**A:**
1. 检查 `build.gradle` 中的 Lombok 配置
2. 确保版本兼容性
3. 执行 `./gradlew clean build --refresh-dependencies`

### Q: IDE 显示错误但命令行编译成功
**A:**
这通常是 IDE 配置问题：
1. 重新导入项目
2. 清理 IDE 缓存
3. 确保 IDE 使用正确的 JDK 版本

## 项目结构

确保您的项目结构如下：
```
learn-distribute-trasaction/
├── build.gradle                 # 根构建文件
├── settings.gradle              # 项目设置
├── gradle/
│   └── libs.versions.toml      # 版本目录
├── AccountService/
│   └── build.gradle            # 子模块构建文件
├── OrderService/
│   └── build.gradle
├── StockService/
│   └── build.gradle
└── .vscode/
    └── settings.json           # VS Code 配置
```

## 测试 Lombok 配置

创建一个简单的测试类来验证 Lombok 是否工作：

```java
import lombok.extern.slf4j.Slf4j;
import lombok.Data;

@Slf4j
@Data
public class TestLombok {
    private String name;
    private int age;
    
    public void test() {
        log.info("Lombok is working!");
        log.debug("Name: {}, Age: {}", name, age);
    }
}
```

如果 IDE 能够识别 `log` 变量和自动生成的 getter/setter 方法，说明配置成功。

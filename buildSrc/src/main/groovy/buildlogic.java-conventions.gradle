/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    id 'java-library'
    id 'maven-publish'
}

repositories {
    mavenLocal()
    maven {
        url = uri('https://repo.maven.apache.org/maven2/')
    }
}

dependencies {
    api 'org.springframework.boot:spring-boot-starter-web:2.7.18'
    api 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2021.0.6.2'
    api 'org.springframework.cloud:spring-cloud-starter-loadbalancer:3.1.8'
    api 'com.mysql:mysql-connector-j:8.0.33'
    api 'com.baomidou:mybatis-plus-boot-starter:3.5.7'
    api 'com.alibaba.cloud:spring-cloud-starter-alibaba-seata:2021.0.6.2'
    api 'io.seata:seata-spring-boot-starter:1.8.0'
    api 'org.projectlombok:lombok:1.18.34'
}

group = 'com.jobs'
version = '1.0'
java.sourceCompatibility = JavaVersion.VERSION_1_8

publishing {
    publications {
        maven(MavenPublication) {
            from(components.java)
        }
    }
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

tasks.withType(Javadoc) {
    options.encoding = 'UTF-8'
}

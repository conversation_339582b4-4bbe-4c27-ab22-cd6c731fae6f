[versions]
springBoot = "2.7.18"
springCloud = "2021.0.9"
springCloudAlibaba = "2021.0.6.2"
mybatisPlus = "3.5.7"
mysql = "8.0.33"
seata = "1.8.0"
lombok = "1.18.34"

[libraries]
# Spring Boot
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test" }

# Spring Cloud
spring-cloud-starter-openfeign = { module = "org.springframework.cloud:spring-cloud-starter-openfeign" }
spring-cloud-starter-loadbalancer = { module = "org.springframework.cloud:spring-cloud-starter-loadbalancer" }

# Spring Cloud Alibaba
nacos-discovery = { module = "com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery" }
alibaba-seata = { module = "com.alibaba.cloud:spring-cloud-starter-alibaba-seata" }

# Database
mysql-connector = { module = "com.mysql:mysql-connector-j", version.ref = "mysql" }
mybatis-plus = { module = "com.baomidou:mybatis-plus-boot-starter", version.ref = "mybatisPlus" }

# Seata
seata-starter = { module = "io.seata:seata-spring-boot-starter", version.ref = "seata" }

# Tools
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }
